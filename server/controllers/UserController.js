const User = require('../models/User');

class UserController {
  // Get current user profile
  static async getProfile(req, res) {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User profile not found'
      });
    }

    res.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        notificationEnabled: user.notification_enabled,
        preferredLanguage: user.preferred_language,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    });
  }

  // Update current user profile
  static async updateProfile(req, res) {
    const { firstName, lastName, email, notificationEnabled, preferredLanguage } = req.body;
    const userId = req.user.id;

    // Check if email is being changed and if it already exists
    if (email) {
      const emailExists = await User.emailExists(email, userId);
      if (emailExists) {
        return res.status(409).json({
          error: 'Email already exists',
          message: 'An account with this email already exists'
        });
      }
    }

    // Prepare update data
    const updateData = {};
    if (firstName !== undefined) updateData.first_name = firstName;
    if (lastName !== undefined) updateData.last_name = lastName;
    if (email !== undefined) updateData.email = email;
    if (notificationEnabled !== undefined) updateData.notification_enabled = notificationEnabled;
    if (preferredLanguage !== undefined) updateData.preferred_language = preferredLanguage;

    const updatedUser = await User.updateProfile(userId, updateData);

    if (!updatedUser) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User profile not found'
      });
    }

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        notificationEnabled: updatedUser.notification_enabled,
        preferredLanguage: updatedUser.preferred_language,
        updatedAt: updatedUser.updated_at
      }
    });
  }

  // Update password
  static async updatePassword(req, res) {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Current password and new password are required'
      });
    }

    // Get user with password hash
    const user = await User.findByEmail(req.user.email);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User not found'
      });
    }

    // Verify current password
    const isValidPassword = await User.verifyPassword(currentPassword, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Current password is incorrect'
      });
    }

    // Update password
    await User.updatePassword(userId, newPassword);

    res.json({
      message: 'Password updated successfully'
    });
  }

  // Get all users (admin only)
  static async getAllUsers(req, res) {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await User.findAll(page, limit);

    res.json({
      message: 'Users retrieved successfully',
      ...result
    });
  }

  // Get user by ID (admin only)
  static async getUserById(req, res) {
    const { id } = req.params;

    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User with specified ID not found'
      });
    }

    res.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        notificationEnabled: user.notification_enabled,
        preferredLanguage: user.preferred_language,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    });
  }

  // Delete user (admin only)
  static async deleteUser(req, res) {
    const { id } = req.params;

    // Prevent self-deletion
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({
        error: 'Invalid operation',
        message: 'Cannot delete your own account'
      });
    }

    const deletedUser = await User.delete(id);
    
    if (!deletedUser) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User with specified ID not found'
      });
    }

    res.json({
      message: 'User deleted successfully',
      deletedUserId: deletedUser.id
    });
  }
}

module.exports = UserController;
